# SQLPage 安装与部署指南

> 详细介绍 SQLPage 的安装、配置和部署方法，帮助您快速搭建 SQL 网站。

## 📚 目录

### 💾 [安装 SQLPage](#安装-sqlpage)
- 下载和安装
- 不同操作系统的安装方法
- 使用包管理器安装

### ⚙️ [配置 SQLPage](#配置-sqlpage)
- 基本配置
- 数据库连接
- 自定义设置

### 🚀 [本地开发](#本地开发)
- 创建第一个网站
- 目录结构
- 开发工作流

### 🌐 [部署上线](#部署上线)
- 使用 DataPage.app 托管
- 自建服务器部署
- Docker 部署

---

## 💾 安装 SQLPage

### 下载安装包

访问 [SQLPage GitHub Releases](https://github.com/sqlpage/SQLPage/releases) 页面，下载适合您操作系统的版本：

- **Windows**: `sqlpage-windows.zip`
- **Linux**: `sqlpage-linux.tgz`
- **macOS**: `sqlpage-macos.tgz`

### 各操作系统安装方法

#### Windows 安装
1. 下载 `sqlpage-windows.zip`
2. 解压到您的项目文件夹
3. 双击 `sqlpage.exe` 启动服务器

#### Linux 安装
1. 下载 `sqlpage-linux.tgz`
2. 解压：`tar -xzf sqlpage-linux.tgz`
3. 添加执行权限：`chmod +x sqlpage`
4. 运行：`./sqlpage`

#### macOS 安装
1. 使用 Homebrew（推荐）：
   ```bash
   brew install sqlpage
   ```
2. 或下载 `sqlpage-macos.tgz` 手动安装

### 使用包管理器安装

#### Homebrew (macOS/Linux)
```bash
brew install sqlpage
```

#### Docker
```bash
# 拉取镜像
docker pull lovasoa/sqlpage

# 运行容器
docker run -p 8080:8080 -v $(pwd):/var/www lovasoa/sqlpage
```

#### Cargo (Rust)
```bash
cargo install sqlpage
```

#### Scoop (Windows)
```bash
scoop install sqlpage
```

#### Nix
```bash
nix-env -iA nixpkgs.sqlpage
```

---

## ⚙️ 配置 SQLPage

### 基本配置文件

在项目根目录创建 `sqlpage/sqlpage.json` 配置文件：

```json
{
  "listen_on": "0.0.0.0:8080",
  "database_url": "sqlite://sqlpage.db",
  "web_root": ".",
  "site_prefix": "",
  "max_uploaded_file_size": 5242880,
  "https_domain": null,
  "https_certificate_file": null,
  "https_private_key_file": null
}
```

### 数据库连接配置

#### SQLite（默认）
```json
{
  "database_url": "sqlite://sqlpage.db"
}
```

#### PostgreSQL
```json
{
  "database_url": "postgres://username:password@localhost/database_name"
}
```

#### MySQL
```json
{
  "database_url": "mysql://username:password@localhost/database_name"
}
```

#### SQL Server
```json
{
  "database_url": "mssql://username:password@localhost/database_name"
}
```

### 高级配置选项

```json
{
  "listen_on": "127.0.0.1:8080",
  "database_url": "sqlite://sqlpage.db",
  "web_root": "./web",
  "site_prefix": "/app",
  "max_uploaded_file_size": 10485760,
  "allow_exec": false,
  "environment": "production",
  "https_domain": "example.com",
  "https_certificate_file": "/path/to/cert.pem",
  "https_private_key_file": "/path/to/key.pem",
  "https_acme_directory_url": "https://acme-v02.api.letsencrypt.org/directory",
  "https_acme_email": "<EMAIL>"
}
```

**配置说明**：
- `listen_on`: 监听地址和端口
- `web_root`: 网站根目录
- `site_prefix`: URL 前缀
- `max_uploaded_file_size`: 最大上传文件大小（字节）
- `allow_exec`: 是否允许执行系统命令
- `environment`: 运行环境（development/production）

---

## 🚀 本地开发

### 创建项目结构

```
my-sqlpage-website/
├── sqlpage/
│   ├── sqlpage.json          # 配置文件
│   ├── sqlpage.db           # SQLite 数据库（自动创建）
│   ├── migrations/          # 数据库迁移文件
│   │   └── 0001_init.sql
│   └── templates/           # 自定义组件模板
│       └── my_component.handlebars
├── index.sql                # 首页
├── about.sql               # 关于页面
├── static/                 # 静态文件
│   ├── style.css
│   └── script.js
└── sqlpage.exe             # SQLPage 可执行文件（Windows）
```

### 数据库迁移

在 `sqlpage/migrations/` 目录中创建迁移文件：

**0001_create_tables.sql**
```sql
-- 创建用户表
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建文章表
CREATE TABLE posts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    author_id INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (author_id) REFERENCES users(id)
);

-- 插入示例数据
INSERT INTO users (name, email) VALUES 
    ('张三', '<EMAIL>'),
    ('李四', '<EMAIL>');
```

### 开发工作流

1. **启动开发服务器**
   ```bash
   ./sqlpage  # Linux/macOS
   # 或双击 sqlpage.exe (Windows)
   ```

2. **访问网站**
   打开浏览器访问 `http://localhost:8080`

3. **实时开发**
   - 修改 `.sql` 文件后刷新浏览器即可看到变化
   - 无需重启服务器

4. **调试技巧**
   ```sql
   -- 使用 debug 组件查看数据
   SELECT 'debug' AS component;
   SELECT * FROM users;
   
   -- 查看所有变量
   SELECT 'code' AS component,
          'json' AS language,
          sqlpage.variables() AS contents;
   ```

---

## 🌐 部署上线

### 使用 DataPage.app 托管（推荐）

[DataPage.app](https://datapage.app) 是 SQLPage 官方托管服务：

1. **注册账户**
   访问 https://datapage.app 注册账户

2. **上传项目**
   - 将项目文件打包为 ZIP
   - 通过 Web 界面上传
   - 或使用 Git 集成

3. **配置域名**
   - 使用提供的子域名
   - 或绑定自定义域名

4. **数据库管理**
   - 自动提供持久化数据库
   - 支持数据库备份和恢复

### 自建服务器部署

#### 使用 Docker（推荐）

**docker-compose.yml**
```yaml
version: '3.8'
services:
  sqlpage:
    image: lovasoa/sqlpage:latest
    ports:
      - "8080:8080"
    volumes:
      - ./:/var/www
      - ./data:/var/lib/sqlpage
    environment:
      - DATABASE_URL=sqlite:///var/lib/sqlpage/sqlpage.db
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - sqlpage
    restart: unless-stopped
```

**启动服务**
```bash
docker-compose up -d
```

#### 使用 Systemd

**sqlpage.service**
```ini
[Unit]
Description=SQLPage Web Server
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/var/www/sqlpage
ExecStart=/usr/local/bin/sqlpage
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
```

**安装和启动**
```bash
# 复制服务文件
sudo cp sqlpage.service /etc/systemd/system/

# 重载 systemd
sudo systemctl daemon-reload

# 启用并启动服务
sudo systemctl enable sqlpage
sudo systemctl start sqlpage

# 查看状态
sudo systemctl status sqlpage
```

#### Nginx 反向代理配置

**nginx.conf**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 静态文件缓存
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        proxy_pass http://127.0.0.1:8080;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}

# HTTPS 配置
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 生产环境优化

#### 1. 数据库优化
```json
{
  "database_url": "postgres://user:pass@localhost/prod_db?sslmode=require",
  "environment": "production"
}
```

#### 2. 安全配置
```json
{
  "allow_exec": false,
  "max_uploaded_file_size": 5242880,
  "https_domain": "your-domain.com"
}
```

#### 3. 性能优化
- 使用 CDN 加速静态资源
- 启用 Gzip 压缩
- 配置适当的缓存策略
- 使用连接池优化数据库连接

#### 4. 监控和日志
```bash
# 查看 SQLPage 日志
journalctl -u sqlpage -f

# 监控系统资源
htop
```

---

## 🔧 故障排除

### 常见问题

#### 1. 端口被占用
```bash
# 查看端口占用
netstat -tulpn | grep 8080

# 更改端口
echo '{"listen_on": "0.0.0.0:8081"}' > sqlpage/sqlpage.json
```

#### 2. 数据库连接失败
```sql
-- 测试数据库连接
SELECT 'text' AS component,
       'Database connection successful!' AS contents;
```

#### 3. 权限问题
```bash
# 确保文件权限正确
chmod +x sqlpage
chmod -R 755 ./
```

#### 4. 内存不足
```json
{
  "max_uploaded_file_size": 1048576,
  "database_url": "sqlite://sqlpage.db?cache=shared&mode=rwc"
}
```

### 调试技巧

1. **启用详细日志**
   ```bash
   RUST_LOG=debug ./sqlpage
   ```

2. **检查配置**
   ```sql
   SELECT 'debug' AS component;
   SELECT sqlpage.app_config('database_url') AS config_value;
   ```

3. **性能分析**
   ```sql
   SELECT 'text' AS component,
          'Page loaded in: ' || (julianday('now') - julianday($start_time)) * 86400 || ' seconds' AS contents;
   ```

---

## 📚 相关资源

- **官方文档**: https://sql-page.com/documentation.sql
- **GitHub 仓库**: https://github.com/sqlpage/SQLPage
- **配置参考**: https://github.com/sqlpage/SQLPage/blob/main/configuration.md
- **Docker Hub**: https://hub.docker.com/r/lovasoa/sqlpage
- **社区讨论**: https://github.com/sqlpage/SQLPage/discussions

通过本指南，您应该能够成功安装、配置和部署 SQLPage 应用程序。如有问题，请参考官方文档或社区讨论。
