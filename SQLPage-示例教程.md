# SQLPage 示例教程

> 通过实际案例学习 SQLPage 的强大功能，从简单的数据展示到复杂的 Web 应用。

## 📚 教程目录

### 🎯 基础示例
1. [简单数据展示](#简单数据展示)
2. [用户注册表单](#用户注册表单)
3. [数据图表展示](#数据图表展示)

### 🔧 进阶应用
4. [多步骤表单](#多步骤表单)
5. [用户认证系统](#用户认证系统)
6. [文件上传处理](#文件上传处理)

### 🚀 高级功能
7. [仪表板应用](#仪表板应用)
8. [API 接口开发](#api-接口开发)
9. [地图数据可视化](#地图数据可视化)

---

## 🎯 基础示例

### 简单数据展示

创建一个显示用户列表的页面：

**users.sql**
```sql
-- 设置页面基本信息
SELECT 'shell' AS component,
       '用户管理系统' AS title,
       'zh-CN' AS lang;

-- 页面标题
SELECT 'title' AS component,
       '用户列表' AS contents,
       1 AS level;

-- 用户统计卡片
SELECT 'card' AS component,
       '系统统计' AS title,
       4 AS columns;

SELECT '总用户数' AS title,
       COUNT(*) AS description,
       'users' AS icon,
       'primary' AS color
FROM users;

SELECT '活跃用户' AS title,
       COUNT(*) AS description,
       'user-check' AS icon,
       'success' AS color
FROM users WHERE active = 1;

SELECT '今日注册' AS title,
       COUNT(*) AS description,
       'user-plus' AS icon,
       'info' AS color
FROM users WHERE DATE(created_at) = DATE('now');

SELECT '待审核' AS title,
       COUNT(*) AS description,
       'clock' AS icon,
       'warning' AS color
FROM users WHERE status = 'pending';

-- 用户数据表格
SELECT 'table' AS component,
       '用户详情' AS title,
       TRUE AS striped,
       TRUE AS hover;

SELECT 
    id AS ID,
    name AS 姓名,
    email AS 邮箱,
    CASE 
        WHEN active = 1 THEN '✅ 活跃'
        ELSE '❌ 禁用'
    END AS 状态,
    created_at AS 注册时间,
    CONCAT('[编辑](edit_user.sql?id=', id, ')') AS 操作
FROM users
ORDER BY created_at DESC
LIMIT 50;
```

### 用户注册表单

创建用户注册功能：

**register.sql**
```sql
SELECT 'shell' AS component,
       '用户注册' AS title;

-- 显示错误信息（如果有）
SELECT 'alert' AS component,
       'danger' AS color,
       '注册失败' AS title,
       $error AS description
WHERE $error IS NOT NULL;

-- 注册表单
SELECT 'form' AS component,
       '创建新账户' AS title,
       'register_process.sql' AS action,
       'POST' AS method;

SELECT 'name' AS name,
       'text' AS type,
       '姓名' AS label,
       '请输入您的姓名' AS placeholder,
       TRUE AS required;

SELECT 'email' AS name,
       'email' AS type,
       '邮箱地址' AS label,
       '请输入有效的邮箱地址' AS placeholder,
       TRUE AS required;

SELECT 'password' AS name,
       'password' AS type,
       '密码' AS label,
       '密码长度至少8位' AS placeholder,
       8 AS minlength,
       TRUE AS required;

SELECT 'confirm_password' AS name,
       'password' AS type,
       '确认密码' AS label,
       '请再次输入密码' AS placeholder,
       TRUE AS required;

SELECT 'terms' AS name,
       'checkbox' AS type,
       '我同意服务条款' AS label,
       TRUE AS required;

SELECT 'submit' AS name,
       'submit' AS type,
       '注册' AS value,
       'primary' AS color;
```

**register_process.sql**
```sql
-- 验证密码匹配
SELECT 'redirect' AS component,
       'register.sql?error=密码不匹配' AS link
WHERE $password != $confirm_password;

-- 检查邮箱是否已存在
SELECT 'redirect' AS component,
       'register.sql?error=邮箱已被注册' AS link
WHERE EXISTS (SELECT 1 FROM users WHERE email = $email);

-- 插入新用户
INSERT INTO users (name, email, password_hash, created_at)
VALUES (
    $name,
    $email,
    sqlpage.hash_password($password),
    datetime('now')
);

-- 重定向到成功页面
SELECT 'redirect' AS component,
       'login.sql?message=注册成功，请登录' AS link;
```

### 数据图表展示

创建销售数据仪表板：

**dashboard.sql**
```sql
SELECT 'shell' AS component,
       '销售仪表板' AS title;

-- 关键指标
SELECT 'big_number' AS component,
       '本月业绩' AS title;

SELECT 
    '总销售额' AS title,
    CONCAT('¥', FORMAT(SUM(amount), 2)) AS value,
    CASE 
        WHEN SUM(amount) > 100000 THEN 'success'
        WHEN SUM(amount) > 50000 THEN 'warning'
        ELSE 'danger'
    END AS color,
    CONCAT(
        ROUND((SUM(amount) - LAG(SUM(amount)) OVER ()) / LAG(SUM(amount)) OVER () * 100, 1),
        '%'
    ) AS change
FROM sales 
WHERE strftime('%Y-%m', created_at) = strftime('%Y-%m', 'now');

-- 月度销售趋势图
SELECT 'chart' AS component,
       '月度销售趋势' AS title,
       'line' AS type,
       TRUE AS toolbar;

SELECT 
    strftime('%Y-%m', created_at) AS x,
    SUM(amount) AS y
FROM sales
WHERE created_at >= date('now', '-12 months')
GROUP BY strftime('%Y-%m', created_at)
ORDER BY x;

-- 产品销售排行
SELECT 'chart' AS component,
       '产品销售排行' AS title,
       'bar' AS type,
       TRUE AS horizontal,
       TRUE AS labels;

SELECT 
    product_name AS label,
    SUM(quantity) AS value
FROM sales s
JOIN products p ON s.product_id = p.id
WHERE s.created_at >= date('now', '-30 days')
GROUP BY product_name
ORDER BY SUM(quantity) DESC
LIMIT 10;
```

---

## 🔧 进阶应用

### 多步骤表单

基于官方示例的航班预订系统：

**book_flight.sql**
```sql
SELECT 'shell' AS component,
       '航班预订' AS title;

-- 步骤指示器
SELECT 'steps' AS component;
SELECT '选择航班' AS title, 1 AS step, ($step IS NULL OR $step = '1') AS active;
SELECT '乘客信息' AS title, 2 AS step, $step = '2' AS active;
SELECT '确认订单' AS title, 3 AS step, $step = '3' AS active;

-- 步骤1：选择航班
SELECT 'form' AS component,
       '选择您的航班' AS title,
       'book_flight.sql' AS action
WHERE $step IS NULL OR $step = '1';

SELECT 'from_city' AS name,
       'select' AS type,
       '出发城市' AS label,
       'Beijing,Shanghai,Guangzhou,Shenzhen' AS options,
       $from_city AS value
WHERE $step IS NULL OR $step = '1';

SELECT 'to_city' AS name,
       'select' AS type,
       '到达城市' AS label,
       'Beijing,Shanghai,Guangzhou,Shenzhen' AS options,
       $to_city AS value
WHERE $step IS NULL OR $step = '1';

SELECT 'departure_date' AS name,
       'date' AS type,
       '出发日期' AS label,
       date('now', '+1 day') AS min,
       $departure_date AS value
WHERE $step IS NULL OR $step = '1';

SELECT 'adults' AS name,
       'number' AS type,
       '成人数量' AS label,
       1 AS min,
       9 AS max,
       COALESCE($adults, 1) AS value
WHERE $step IS NULL OR $step = '1';

SELECT 'children' AS name,
       'number' AS type,
       '儿童数量' AS label,
       0 AS min,
       9 AS max,
       COALESCE($children, 0) AS value
WHERE $step IS NULL OR $step = '1';

SELECT 'step' AS name,
       'hidden' AS type,
       '2' AS value
WHERE $step IS NULL OR $step = '1';

-- 步骤2：乘客信息
SELECT 'form' AS component,
       '乘客信息' AS title,
       'book_flight.sql' AS action
WHERE $step = '2';

-- 动态生成乘客信息字段
SELECT 
    CONCAT('passenger_', passenger_num, '_name') AS name,
    'text' AS type,
    CONCAT('乘客 ', passenger_num, ' 姓名') AS label,
    TRUE AS required
FROM (
    SELECT ROW_NUMBER() OVER () AS passenger_num
    FROM (
        SELECT 1 AS n UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL 
        SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL 
        SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9
    ) numbers
    WHERE passenger_num <= COALESCE($adults, 1) + COALESCE($children, 0)
) passengers
WHERE $step = '2';

-- 隐藏字段传递数据
SELECT 'from_city' AS name, 'hidden' AS type, $from_city AS value WHERE $step = '2';
SELECT 'to_city' AS name, 'hidden' AS type, $to_city AS value WHERE $step = '2';
SELECT 'departure_date' AS name, 'hidden' AS type, $departure_date AS value WHERE $step = '2';
SELECT 'adults' AS name, 'hidden' AS type, $adults AS value WHERE $step = '2';
SELECT 'children' AS name, 'hidden' AS type, $children AS value WHERE $step = '2';
SELECT 'step' AS name, 'hidden' AS type, '3' AS value WHERE $step = '2';

-- 步骤3：确认订单
SELECT 'card' AS component,
       '订单确认' AS title
WHERE $step = '3';

SELECT 
    '航班信息' AS title,
    CONCAT($from_city, ' → ', $to_city) AS description,
    $departure_date AS footer
WHERE $step = '3';

-- 处理最终提交
INSERT INTO flight_bookings (
    from_city, to_city, departure_date, 
    adults, children, passenger_info, 
    created_at
)
SELECT 
    $from_city, $to_city, $departure_date,
    $adults, $children,
    json_object(
        'passengers', json_array(
            -- 这里需要根据实际乘客数量动态构建
            json_object('name', $passenger_1_name)
        )
    ),
    datetime('now')
WHERE $step = '3';

SELECT 'alert' AS component,
       'success' AS color,
       '预订成功！' AS title,
       '您的航班已成功预订，确认邮件将发送到您的邮箱。' AS description
WHERE $step = '3';
```

### 用户认证系统

完整的登录/注销系统：

**login.sql**
```sql
-- 如果已登录，重定向到仪表板
SELECT 'redirect' AS component, 'dashboard.sql' AS link
WHERE sqlpage.cookie('session_token') IS NOT NULL
AND EXISTS (
    SELECT 1 FROM user_sessions 
    WHERE token = sqlpage.cookie('session_token')
    AND expires_at > datetime('now')
);

SELECT 'shell' AS component, '用户登录' AS title;

-- 显示消息
SELECT 'alert' AS component,
       'success' AS color,
       $message AS title
WHERE $message IS NOT NULL;

SELECT 'alert' AS component,
       'danger' AS color,
       '登录失败' AS title,
       $error AS description
WHERE $error IS NOT NULL;

-- 登录表单
SELECT 'form' AS component,
       '登录您的账户' AS title,
       'login_process.sql' AS action;

SELECT 'email' AS name,
       'email' AS type,
       '邮箱地址' AS label,
       TRUE AS required;

SELECT 'password' AS name,
       'password' AS type,
       '密码' AS label,
       TRUE AS required;

SELECT 'remember' AS name,
       'checkbox' AS type,
       '记住我' AS label;

SELECT 'submit' AS name,
       'submit' AS type,
       '登录' AS value,
       'primary' AS color;

-- 注册链接
SELECT 'text' AS component;
SELECT '还没有账户？' AS contents;
SELECT '[立即注册](register.sql)' AS contents, 'primary' AS color;
```

**login_process.sql**
```sql
-- 验证用户凭据
SET user_id = (
    SELECT id FROM users 
    WHERE email = $email 
    AND password_hash = sqlpage.hash_password($password)
    AND active = 1
);

-- 登录失败
SELECT 'redirect' AS component, 
       'login.sql?error=邮箱或密码错误' AS link
WHERE $user_id IS NULL;

-- 生成会话令牌
SET session_token = sqlpage.random_string(32);

-- 设置会话过期时间
SET expires_at = CASE 
    WHEN $remember = 'on' THEN datetime('now', '+30 days')
    ELSE datetime('now', '+1 day')
END;

-- 保存会话到数据库
INSERT INTO user_sessions (user_id, token, expires_at, created_at)
VALUES ($user_id, $session_token, $expires_at, datetime('now'));

-- 设置会话 Cookie
SELECT 'cookie' AS component,
       'session_token' AS name,
       $session_token AS value,
       CASE 
           WHEN $remember = 'on' THEN 2592000  -- 30天
           ELSE 86400  -- 1天
       END AS max_age,
       '/' AS path,
       TRUE AS http_only;

-- 重定向到仪表板
SELECT 'redirect' AS component, 'dashboard.sql' AS link;
```

**logout.sql**
```sql
-- 删除会话记录
DELETE FROM user_sessions 
WHERE token = sqlpage.cookie('session_token');

-- 清除 Cookie
SELECT 'cookie' AS component,
       'session_token' AS name,
       '' AS value,
       0 AS max_age;

-- 重定向到登录页面
SELECT 'redirect' AS component, 
       'login.sql?message=已成功退出登录' AS link;
```

---

## 🚀 高级功能

### 仪表板应用

创建一个完整的管理仪表板：

**admin_dashboard.sql**
```sql
-- 认证检查
SELECT 'authentication' AS component,
       'login.sql' AS link,
       '请先登录' AS message
WHERE NOT EXISTS (
    SELECT 1 FROM user_sessions s
    JOIN users u ON s.user_id = u.id
    WHERE s.token = sqlpage.cookie('session_token')
    AND s.expires_at > datetime('now')
    AND u.role = 'admin'
);

SELECT 'shell' AS component,
       '管理仪表板' AS title,
       'admin' AS menu_item;

-- 导航菜单
SELECT 'tab' AS component;
SELECT '概览' AS title, 'dashboard.sql' AS link, TRUE AS active;
SELECT '用户管理' AS title, 'users.sql' AS link;
SELECT '订单管理' AS title, 'orders.sql' AS link;
SELECT '系统设置' AS title, 'settings.sql' AS link;

-- 关键指标卡片
SELECT 'big_number' AS component;

SELECT 
    '总用户数' AS title,
    COUNT(*) AS value,
    'users' AS icon,
    'primary' AS color,
    CONCAT('+', COUNT(*) - LAG(COUNT(*)) OVER (), ' 本月') AS description
FROM users;

SELECT 
    '总订单数' AS title,
    COUNT(*) AS value,
    'shopping-cart' AS icon,
    'success' AS color
FROM orders;

SELECT 
    '总收入' AS title,
    CONCAT('¥', FORMAT(SUM(total_amount), 2)) AS value,
    'dollar-sign' AS icon,
    'info' AS color
FROM orders WHERE status = 'completed';

-- 图表区域
SELECT 'card' AS component,
       2 AS columns;

-- 用户增长趋势
SELECT '用户增长趋势' AS title,
       '/charts/user_growth.sql?_sqlpage_embed' AS embed;

-- 订单状态分布
SELECT '订单状态分布' AS title,
       '/charts/order_status.sql?_sqlpage_embed' AS embed;

-- 最近活动
SELECT 'timeline' AS component,
       '最近活动' AS title;

SELECT 
    description AS title,
    created_at AS date,
    CASE action_type
        WHEN 'user_register' THEN 'user-plus'
        WHEN 'order_create' THEN 'shopping-cart'
        WHEN 'payment' THEN 'credit-card'
        ELSE 'activity'
    END AS icon
FROM activity_logs
ORDER BY created_at DESC
LIMIT 10;
```

### API 接口开发

创建 RESTful API 接口：

**api/users.sql**
```sql
-- 设置 JSON 响应
SELECT 'json' AS component;

-- API 认证
SELECT json_object('error', 'Unauthorized') AS error
WHERE sqlpage.header('Authorization') != 'Bearer your-api-key';

-- 根据请求方法处理
-- GET: 获取用户列表
SELECT 
    json_object(
        'id', id,
        'name', name,
        'email', email,
        'created_at', created_at
    ) AS user
FROM users
WHERE sqlpage.request_method() = 'GET'
ORDER BY created_at DESC;

-- POST: 创建新用户
INSERT INTO users (name, email, password_hash, created_at)
SELECT 
    json_extract(sqlpage.request_body(), '$.name'),
    json_extract(sqlpage.request_body(), '$.email'),
    sqlpage.hash_password(json_extract(sqlpage.request_body(), '$.password')),
    datetime('now')
WHERE sqlpage.request_method() = 'POST';

SELECT json_object('message', 'User created successfully') AS result
WHERE sqlpage.request_method() = 'POST';
```

### 地图数据可视化

创建交互式地图应用：

**store_locator.sql**
```sql
SELECT 'shell' AS component,
       '门店定位' AS title;

-- 地图组件
SELECT 'map' AS component,
       '我们的门店' AS title,
       39.9042 AS latitude,
       116.4074 AS longitude,
       10 AS zoom;

-- 门店位置数据
SELECT 
    name AS title,
    address AS description,
    latitude,
    longitude,
    'store' AS icon,
    'primary' AS color,
    CONCAT('tel:', phone) AS link
FROM stores
WHERE active = 1;

-- 门店列表
SELECT 'list' AS component,
       '门店详情' AS title;

SELECT 
    name AS title,
    CONCAT(address, ' | 电话: ', phone) AS description,
    CONCAT('map.sql?store_id=', id) AS link
FROM stores
WHERE active = 1
ORDER BY city, name;
```

---

## 💡 最佳实践总结

1. **模块化设计**: 将复杂功能拆分为多个 SQL 文件
2. **安全第一**: 始终验证用户输入和权限
3. **用户体验**: 提供清晰的错误信息和成功反馈
4. **性能优化**: 合理使用索引和分页
5. **代码复用**: 使用 `run_sql()` 函数共享通用代码

通过这些示例，您可以构建从简单的数据展示到复杂的企业级应用。SQLPage 的强大之处在于用纯 SQL 就能实现丰富的 Web 功能！
