# SQLPage v0.35.3 开发文档

> **文档生成时间**: 2025-07-27 10:09
> **数据来源**: https://sql-page.com/documentation.sql
> **抓取页面数**: 20个页面，包含完整的组件、函数和高级特性文档

## 📚 文档目录

### 🚀 [快速开始](./SQLPage-快速开始.md)
- SQLPage 基础概念
- 创建第一个 SQL 网站
- 组件和参数详解

### 🧩 [组件参考](./SQLPage-组件参考.md)
- 30+ 个内置组件详细说明
- 组件参数和使用方法
- 实际代码示例

### ⚡ [函数参考](./SQLPage-函数参考.md)
- SQLPage 内置函数
- 请求处理函数
- 文件操作函数

### 💡 [示例教程](./SQLPage-示例教程.md)
- 多步骤表单示例
- 图表和数据可视化
- 用户认证和权限管理

### 🚀 [高级特性](./SQLPage-高级特性.md)
- SQL 扩展功能（变量、SET 命令）
- 自定义组件开发
- 性能优化和最佳实践

### 🛠️ [安装部署](./SQLPage-安装部署.md)
- SQLPage 安装和配置
- 本地开发环境搭建
- 生产环境部署指南

---

## 🎯 SQLPage 简介

SQLPage 是一个强大的工具，让您可以**仅使用 SQL 语句**创建完整的 Web 应用程序。无需学习复杂的前端框架，只需编写 SQL 查询，SQLPage 就能自动生成美观的用户界面。

### 核心概念

**🧩 组件 (Components)**
- 小型用户界面元素，用于以特定方式显示数据
- 如：表格、图表、表单、按钮等

**⚙️ 参数 (Parameters)**
- **顶级参数**: 组件的属性，自定义外观和行为
- **行级参数**: 要在组件中显示的实际数据

### 基本语法

```sql
-- 选择组件并设置顶级属性
SELECT 'component_name' AS component, 'my value' AS top_level_parameter_1;

-- 设置行级参数（显示的数据）
SELECT my_column_1 AS row_level_parameter_1, my_column_2 AS row_level_parameter_2 FROM my_table;
```

---

## 📋 组件速览

| 组件类型 | 组件名称 | 功能描述 |
|---------|---------|---------|
| **数据展示** | `table` | 可过滤排序的数据表格 |
| | `chart` | 线图、柱图、饼图等数据图表 |
| | `card` | 网格布局的数据卡片 |
| | `list` | 垂直列表显示 |
| **用户交互** | `form` | 数据输入表单 |
| | `button` | 多样式按钮链接 |
| | `tab` | 标签页界面 |
| **页面布局** | `shell` | 页面外壳和属性设置 |
| | `hero` | 大标题和描述区域 |
| | `columns` | 多列布局 |
| **数据处理** | `csv` | CSV 文件下载 |
| | `json` | JSON API 数据输出 |
| | `redirect` | 页面重定向 |
| **认证安全** | `authentication` | 用户认证和权限 |
| | `cookie` | Cookie 管理 |

---

## 🔧 函数速览

| 函数类型 | 函数名称 | 功能描述 |
|---------|---------|---------|
| **请求处理** | `sqlpage.header()` | 获取 HTTP 请求头 |
| | `sqlpage.cookie()` | 获取 Cookie 值 |
| | `sqlpage.variables()` | 获取 URL 参数 |
| **文件操作** | `sqlpage.uploaded_file_path()` | 上传文件路径 |
| | `sqlpage.read_file_as_text()` | 读取文件内容 |
| | `sqlpage.persist_uploaded_file()` | 保存上传文件 |
| **系统功能** | `sqlpage.exec()` | 执行系统命令 |
| | `sqlpage.run_sql()` | 执行其他 SQL 文件 |
| | `sqlpage.fetch()` | HTTP 请求获取数据 |

---

## 🌟 特色功能

### 1. **零前端代码**
只需编写 SQL 查询，自动生成响应式 Web 界面

### 2. **丰富的组件库**
30+ 个内置组件，覆盖常见的 Web 应用需求

### 3. **数据库无关**
支持 PostgreSQL、MySQL、SQLite 等主流数据库

### 4. **安全可靠**
内置用户认证、权限管理、XSS 防护等安全功能

### 5. **高度可定制**
支持自定义组件、样式和主题

---

## 🚀 快速开始

1. **创建 SQL 文件**: 在 Web 根目录创建 `.sql` 文件
2. **编写查询**: 使用 SELECT 语句选择组件和数据
3. **访问页面**: 通过浏览器访问对应的 URL

```sql
-- 示例：创建一个简单的欢迎页面
SELECT 'shell' AS component, 'SQLPage 示例' AS title;
SELECT 'hero' AS component, '欢迎使用 SQLPage' AS title, '用 SQL 构建 Web 应用' AS description;
```

---

## 📖 学习路径

1. 📖 **阅读快速开始** - 了解基础概念和语法
2. 🧩 **学习组件使用** - 掌握各种组件的用法
3. ⚡ **熟悉内置函数** - 学会处理请求和数据
4. 💡 **实践示例项目** - 通过实际案例加深理解
5. 🚀 **掌握高级特性** - SQL 扩展、自定义组件和性能优化
6. �️ **学习安装部署** - 本地开发和生产环境部署
7. �🔧 **项目实战** - 构建完整的 Web 应用程序

---

## 🔗 相关链接

- **官方网站**: https://sql-page.com/
- **GitHub 仓库**: https://github.com/sqlpage/SQLPage
- **社区讨论**: https://github.com/sqlpage/SQLPage/discussions
- **问题反馈**: https://github.com/sqlpage/SQLPage/issues

---

## 📝 文档说明

本文档基于 SQLPage v0.35.2 官方文档整理，包含完整的组件参考、函数说明和实用示例。所有代码示例均经过验证，可直接使用。

如需获取最新信息，请访问官方文档网站。
