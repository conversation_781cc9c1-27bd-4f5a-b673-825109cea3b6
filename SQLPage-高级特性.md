# SQLPage 高级特性

> 深入了解 SQLPage 的高级功能，包括 SQL 扩展、自定义组件开发和性能优化。

## 📚 目录

### 🔧 [SQL 扩展功能](#sql-扩展功能)
- 变量系统
- SET 命令
- 临时表和 JSON 存储

### 🎨 [自定义组件开发](#自定义组件开发)
- 组件模板语法
- Handlebars 助手函数
- 样式和布局

### ⚡ [性能优化](#性能优化)
- 查询优化
- 缓存策略
- 最佳实践

---

## 🔧 SQL 扩展功能

SQLPage 在标准 SQL 基础上添加了一些特殊功能，让您能够更灵活地处理数据和用户交互。

### 变量系统

#### POST 参数变量（冒号前缀）
当用户提交表单时，表单数据会作为变量提供，使用冒号 `:` 前缀：

**form.sql**
```sql
SELECT 'form' AS component,
       'POST' AS method,
       'result.sql' AS action;

SELECT 'age' AS name,
       'number' AS type,
       '您的年龄' AS label;
```

**result.sql**
```sql
SELECT 'text' AS component,
       '您今年 ' || :age || ' 岁了！' AS contents;
```

#### URL 参数变量（美元前缀）
URL 查询参数使用美元符号 `$` 前缀访问：

```sql
-- 访问 /page.sql?user_id=123&category=tech
SELECT 'text' AS component,
       '用户ID: ' || $user_id || ', 分类: ' || $category AS contents;
```

#### SET 命令
SQLPage 重写了 `SET` 语句的行为，用于在 SQLPage 内部存储变量：

```sql
-- 设置默认值
SET user_id = COALESCE($user_id, 0);

-- 复杂计算
SET total_price = (
    SELECT SUM(price * quantity) 
    FROM cart_items 
    WHERE user_id = $user_id
);

-- 条件设置
SET discount_rate = CASE 
    WHEN $total_price > 1000 THEN 0.1
    WHEN $total_price > 500 THEN 0.05
    ELSE 0
END;

-- 使用变量
SELECT 'text' AS component,
       '总价: ¥' || $total_price || ', 折扣: ' || ($discount_rate * 100) || '%' AS contents;
```

### 数据存储方式

#### 1. 临时表存储（推荐用于大数据集）
```sql
-- 清理可能存在的临时表
DROP TABLE IF EXISTS temp_user_stats;

-- 创建临时表
CREATE TEMPORARY TABLE temp_user_stats AS
SELECT 
    user_id,
    COUNT(*) AS order_count,
    SUM(total_amount) AS total_spent
FROM orders
WHERE created_at >= date('now', '-30 days')
GROUP BY user_id;

-- 使用临时表数据
SELECT 'table' AS component, '用户统计' AS title;
SELECT 
    u.name AS 姓名,
    s.order_count AS 订单数量,
    s.total_spent AS 消费总额
FROM temp_user_stats s
JOIN users u ON s.user_id = u.id
ORDER BY s.total_spent DESC;
```

#### 2. JSON 存储（适用于小数据集）
```sql
-- 存储数组数据
SET category_list = (
    SELECT json_group_array(name)
    FROM categories
    WHERE active = 1
);

-- 存储对象数据
SET user_profile = (
    SELECT json_object(
        'id', id,
        'name', name,
        'email', email,
        'level', level
    )
    FROM users
    WHERE id = $user_id
);

-- 使用 JSON 数据
SELECT 'text' AS component,
       '可用分类: ' || $category_list AS contents;
```

### 变量限制和注意事项

1. **数据类型**: 变量只能存储字符串或 NULL
2. **表值结果**: 只会存储第一行第一列的值
3. **作用域**: 变量在整个页面执行期间有效

```sql
-- 错误示例：尝试存储多行数据
SET all_users = (SELECT * FROM users); -- 只会存储第一个用户的 ID

-- 正确示例：使用 JSON 存储多行数据
SET all_users = (
    SELECT json_group_array(
        json_object('id', id, 'name', name)
    )
    FROM users
);
```

---

## 🎨 自定义组件开发

如果内置组件无法满足您的需求，可以创建自定义组件。

### 组件文件结构

在项目根目录创建 `sqlpage/templates/` 文件夹，并在其中放置 `.handlebars` 文件：

```
project/
├── sqlpage/
│   └── templates/
│       ├── my_component.handlebars
│       ├── custom_shell.handlebars
│       └── enhanced_table.handlebars
├── index.sql
└── other_pages.sql
```

### 基本组件模板

**sqlpage/templates/my_list.handlebars**
```handlebars
<div class="card">
    <div class="card-header">
        <h3 class="card-title">{{title}}</h3>
        {{#if description}}
        <p class="text-muted">{{description}}</p>
        {{/if}}
    </div>
    <div class="card-body">
        <ul class="list-group list-group-flush">
        {{#each_row}}
            <li class="list-group-item d-flex justify-content-between align-items-center">
                <div>
                    <strong>{{name}}</strong>
                    {{#if description}}
                    <br><small class="text-muted">{{description}}</small>
                    {{/if}}
                </div>
                {{#if badge}}
                <span class="badge bg-{{default color 'primary'}}">{{badge}}</span>
                {{/if}}
            </li>
        {{/each_row}}
        </ul>
    </div>
</div>
```

**使用自定义组件**
```sql
SELECT 'my_list' AS component,
       '任务列表' AS title,
       '今日待办事项' AS description;

SELECT 
    task_name AS name,
    task_description AS description,
    priority AS badge,
    CASE priority
        WHEN 'high' THEN 'danger'
        WHEN 'medium' THEN 'warning'
        ELSE 'success'
    END AS color
FROM tasks
WHERE due_date = date('now')
ORDER BY priority DESC;
```

### 自定义 Shell 组件

**sqlpage/templates/admin_shell.handlebars**
```handlebars
<!DOCTYPE html>
<html lang="{{default lang 'zh-CN'}}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{default title "管理后台"}}</title>
    <link href="{{static_path 'sqlpage.css'}}" rel="stylesheet">
    {{#if css}}
    <link href="{{css}}" rel="stylesheet">
    {{/if}}
</head>
<body class="theme-dark">
    <div class="page">
        <!-- 侧边栏 -->
        <aside class="navbar navbar-vertical navbar-expand-lg navbar-dark">
            <div class="container-fluid">
                <h1 class="navbar-brand">
                    <a href="/">管理后台</a>
                </h1>
                <div class="navbar-nav">
                    <a class="nav-link" href="/dashboard.sql">
                        <span class="nav-link-icon">📊</span>
                        <span class="nav-link-title">仪表板</span>
                    </a>
                    <a class="nav-link" href="/users.sql">
                        <span class="nav-link-icon">👥</span>
                        <span class="nav-link-title">用户管理</span>
                    </a>
                    <a class="nav-link" href="/orders.sql">
                        <span class="nav-link-icon">🛒</span>
                        <span class="nav-link-title">订单管理</span>
                    </a>
                </div>
            </div>
        </aside>
        
        <!-- 主内容区 -->
        <div class="page-wrapper">
            <div class="container-xl">
                {{#each_row}}{{/each_row}}
            </div>
        </div>
    </div>
    
    <script src="{{static_path 'sqlpage.js'}}" nonce="{{@csp_nonce}}"></script>
    {{#if javascript}}
    <script src="{{javascript}}" nonce="{{@csp_nonce}}"></script>
    {{/if}}
</body>
</html>
```

**使用自定义 Shell**
```sql
SELECT 'admin_shell' AS component,
       '用户管理' AS title;

-- 其他组件...
```

### Handlebars 助手函数

SQLPage 提供了丰富的助手函数：

#### 条件判断
```handlebars
{{#if (gt order_count 10)}}
    <span class="badge bg-success">VIP 客户</span>
{{else if (gt order_count 5)}}
    <span class="badge bg-warning">优质客户</span>
{{else}}
    <span class="badge bg-secondary">普通客户</span>
{{/if}}

{{#if (and (gte age 18) (lt age 65))}}
    <span>工作年龄</span>
{{/if}}
```

#### 数据处理
```handlebars
<!-- 字符串长度 -->
<p>描述长度: {{len description}} 字符</p>

<!-- JSON 处理 -->
{{#each (parse_json tags)}}
    <span class="badge">{{this}}</span>
{{/each}}

<!-- 数组操作 -->
{{#each (sort categories)}}
    <option value="{{this}}">{{this}}</option>
{{/each}}
```

#### 数学运算
```handlebars
<!-- 计算总价 -->
<p>总价: ¥{{plus price (multiply price tax_rate)}}</p>

<!-- 求和 -->
<p>总计: {{sum prices}}</p>
```

#### 日期和 URL 处理
```handlebars
<!-- 格式化日期 -->
<time>{{rfc2822_date created_at}}</time>

<!-- URL 编码 -->
<a href="/search.sql?q={{url_encode search_term}}">搜索</a>
```

### 高级组件示例

**sqlpage/templates/dashboard_card.handlebars**
```handlebars
<div class="col-sm-6 col-lg-3">
    <div class="card card-sm">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col-auto">
                    <span class="bg-{{default color 'primary'}} text-white avatar">
                        {{icon_img icon}}
                    </span>
                </div>
                <div class="col">
                    <div class="font-weight-medium">
                        {{value}}
                        {{#if change}}
                        <span class="text-{{#if (gt change 0)}}success{{else}}danger{{/if}} ms-2">
                            {{#if (gt change 0)}}+{{/if}}{{change}}%
                        </span>
                        {{/if}}
                    </div>
                    <div class="text-muted">{{title}}</div>
                </div>
            </div>
            {{#if progress}}
            <div class="progress progress-sm mt-2">
                <div class="progress-bar bg-{{default color 'primary'}}" 
                     style="width: {{progress}}%" 
                     role="progressbar">
                </div>
            </div>
            {{/if}}
        </div>
    </div>
</div>
```

**使用仪表板卡片**
```sql
SELECT 'dashboard_card' AS component;

SELECT 
    '总用户数' AS title,
    COUNT(*) AS value,
    'users' AS icon,
    'primary' AS color,
    ROUND((COUNT(*) - LAG(COUNT(*)) OVER ()) * 100.0 / LAG(COUNT(*)) OVER (), 1) AS change,
    ROUND(COUNT(*) * 100.0 / 10000, 1) AS progress
FROM users;
```

---

## ⚡ 性能优化

### 查询优化

#### 1. 使用索引
```sql
-- 为经常查询的列创建索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_orders_user_date ON orders(user_id, created_at);

-- 复合索引优化多条件查询
CREATE INDEX idx_products_category_price ON products(category_id, price);
```

#### 2. 分页查询
```sql
-- 使用 LIMIT 和 OFFSET 进行分页
SET page_size = 20;
SET offset_value = ($page - 1) * $page_size;

SELECT 'table' AS component, '用户列表' AS title;
SELECT id, name, email, created_at
FROM users
ORDER BY created_at DESC
LIMIT $page_size OFFSET $offset_value;
```

#### 3. 条件过滤
```sql
-- 尽早过滤数据
SELECT 'chart' AS component, '活跃用户趋势' AS title;
SELECT 
    DATE(login_time) AS x,
    COUNT(DISTINCT user_id) AS y
FROM user_sessions
WHERE login_time >= date('now', '-30 days')  -- 先过滤日期
  AND user_id IS NOT NULL                    -- 再过滤有效用户
GROUP BY DATE(login_time)
ORDER BY x;
```

### 缓存策略

#### 1. HTTP 缓存头
```sql
-- 设置缓存策略
SELECT 'http_header' AS component,
       'Cache-Control' AS "Cache-Control",
       'public, max-age=3600' AS value;  -- 缓存1小时

-- 静态内容长期缓存
SELECT 'http_header' AS component,
       'Cache-Control' AS "Cache-Control",
       'public, max-age=31536000' AS value  -- 缓存1年
WHERE $path LIKE '%.css' OR $path LIKE '%.js' OR $path LIKE '%.png';
```

#### 2. 数据库连接复用
```sql
-- 使用临时表避免重复查询
DROP TABLE IF EXISTS temp_popular_products;
CREATE TEMPORARY TABLE temp_popular_products AS
SELECT product_id, COUNT(*) as order_count
FROM order_items
WHERE created_at >= date('now', '-7 days')
GROUP BY product_id
ORDER BY order_count DESC
LIMIT 10;

-- 多个组件使用同一份数据
SELECT 'card' AS component, '热门商品' AS title;
SELECT p.name AS title, t.order_count AS description
FROM temp_popular_products t
JOIN products p ON t.product_id = p.id;
```

### 最佳实践

#### 1. 避免 N+1 查询
```sql
-- 错误：会产生 N+1 查询
SELECT 'list' AS component;
SELECT 
    name AS title,
    (SELECT COUNT(*) FROM orders WHERE user_id = users.id) AS description
FROM users;

-- 正确：使用 JOIN 一次性获取数据
SELECT 'list' AS component;
SELECT 
    u.name AS title,
    COALESCE(o.order_count, 0) AS description
FROM users u
LEFT JOIN (
    SELECT user_id, COUNT(*) as order_count
    FROM orders
    GROUP BY user_id
) o ON u.id = o.user_id;
```

#### 2. 合理使用变量
```sql
-- 避免重复计算
SET current_month = strftime('%Y-%m', 'now');
SET last_month = strftime('%Y-%m', 'now', '-1 month');

-- 使用变量而不是重复计算
SELECT 'big_number' AS component;
SELECT 
    '本月销售额' AS title,
    (SELECT SUM(amount) FROM sales WHERE strftime('%Y-%m', created_at) = $current_month) AS value;
SELECT 
    '上月销售额' AS title,
    (SELECT SUM(amount) FROM sales WHERE strftime('%Y-%m', created_at) = $last_month) AS value;
```

#### 3. 组件复用
```sql
-- 创建可复用的组件文件
-- common/user_stats.sql
SELECT 'big_number' AS component;
SELECT 
    '总用户数' AS title,
    COUNT(*) AS value,
    'users' AS icon
FROM users;

-- 在多个页面中复用
SELECT 'dynamic' AS component,
       sqlpage.run_sql('common/user_stats.sql') AS properties;
```

---

## 🔗 相关资源

- **官方文档**: https://sql-page.com/documentation.sql
- **组件源码**: https://github.com/sqlpage/SQLPage/tree/main/sqlpage/templates
- **性能指南**: https://sql-page.com/performance.sql
- **安全指南**: https://sql-page.com/safety.sql

通过掌握这些高级特性，您可以构建更加强大、高效和美观的 SQLPage 应用程序！
