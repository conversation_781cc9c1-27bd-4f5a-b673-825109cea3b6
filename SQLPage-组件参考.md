# SQLPage 组件参考手册

> SQLPage 提供了 30+ 个内置组件，涵盖数据展示、用户交互、页面布局等各个方面。

## 📋 组件分类

### 🎨 页面布局组件
- [`shell`](#shell) - 页面外壳和基本属性
- [`hero`](#hero) - 大标题和描述区域
- [`columns`](#columns) - 多列布局
- [`divider`](#divider) - 分隔线

### 📊 数据展示组件
- [`table`](#table) - 数据表格（可过滤排序）
- [`chart`](#chart) - 图表（线图、柱图、饼图等）
- [`card`](#card) - 网格卡片布局
- [`list`](#list) - 垂直列表
- [`datagrid`](#datagrid) - 数据网格
- [`big_number`](#big_number) - 关键指标展示

### 🖱️ 用户交互组件
- [`form`](#form) - 数据输入表单
- [`button`](#button) - 按钮链接
- [`tab`](#tab) - 标签页界面
- [`modal`](#modal) - 模态对话框
- [`foldable`](#foldable) - 可折叠列表

### 🔐 认证安全组件
- [`authentication`](#authentication) - 用户认证
- [`cookie`](#cookie) - Cookie 管理

### 📁 数据处理组件
- [`csv`](#csv) - CSV 文件下载
- [`json`](#json) - JSON API 输出
- [`redirect`](#redirect) - 页面重定向
- [`http_header`](#http_header) - HTTP 头设置

---

## 🎨 页面布局组件

### shell
**页面外壳组件** - 设置整个页面的基本属性

```sql
SELECT 'shell' AS component,
       'My App' AS title,
       'My Application Description' AS description,
       '/favicon.ico' AS icon,
       'zh-CN' AS lang,
       'dark' AS theme,
       '/custom.css' AS css,
       'https://example.com' AS link;
```

**主要参数**:
- `title`: 页面标题
- `description`: 页面描述（SEO）
- `icon`: 网站图标
- `lang`: 页面语言
- `theme`: 主题（light/dark）
- `css`: 自定义 CSS 文件

### hero
**英雄区域组件** - 显示大标题和描述

```sql
SELECT 'hero' AS component,
       '欢迎使用 SQLPage' AS title,
       '用 SQL 构建强大的 Web 应用' AS description,
       'https://example.com/hero-image.jpg' AS image,
       'primary' AS color;
```

**主要参数**:
- `title`: 主标题
- `description`: 描述文字
- `image`: 背景图片
- `color`: 主题颜色

---

## 📊 数据展示组件

### table
**数据表格组件** - 显示结构化数据，支持过滤和排序

```sql
-- 设置表格属性
SELECT 'table' AS component,
       '用户列表' AS title,
       TRUE AS striped,
       TRUE AS hover,
       TRUE AS bordered,
       TRUE AS small;

-- 显示数据（任何列名都会成为表格列）
SELECT id AS ID,
       name AS 姓名,
       email AS 邮箱,
       created_at AS 创建时间,
       CASE WHEN active THEN '✅ 活跃' ELSE '❌ 禁用' END AS 状态
FROM users
ORDER BY created_at DESC;
```

**主要参数**:
- `title`: 表格标题
- `striped`: 斑马纹样式
- `hover`: 悬停效果
- `bordered`: 边框样式
- `small`: 紧凑模式

### chart
**图表组件** - 支持多种图表类型

#### 柱状图示例
```sql
SELECT 'chart' AS component,
       '月度销售额' AS title,
       'bar' AS type,
       TRUE AS labels,
       TRUE AS toolbar;

SELECT '一月' AS label, 15000 AS value
UNION ALL SELECT '二月' AS label, 23000 AS value
UNION ALL SELECT '三月' AS label, 18000 AS value;
```

#### 饼图示例
```sql
SELECT 'chart' AS component,
       '市场份额' AS title,
       'pie' AS type,
       TRUE AS labels;

SELECT 'Chrome' AS label, 65 AS value
UNION ALL SELECT 'Firefox' AS label, 20 AS value
UNION ALL SELECT 'Safari' AS label, 15 AS value;
```

#### 时间序列图表
```sql
SELECT 'chart' AS component,
       '季度收入' AS title,
       'area' AS type,
       'blue-lt' AS color,
       5 AS marker,
       TRUE AS time;

SELECT '2023-01-01T00:00:00Z' AS x, 15 AS y
UNION ALL SELECT '2023-04-01T00:00:00Z' AS x, 46 AS y
UNION ALL SELECT '2023-07-01T00:00:00Z' AS x, 23 AS y;
```

**图表类型**:
- `bar`: 柱状图
- `line`: 线图
- `area`: 面积图
- `pie`: 饼图
- `scatter`: 散点图
- `heatmap`: 热力图
- `treemap`: 树状图

### card
**卡片组件** - 网格布局显示数据

```sql
SELECT 'card' AS component,
       '产品展示' AS title,
       3 AS columns,
       2 AS columns_on_tablet,
       1 AS columns_on_mobile;

SELECT product_name AS title,
       description,
       CONCAT('¥', price) AS footer,
       product_image AS image,
       product_url AS link
FROM products
WHERE featured = TRUE;
```

---

## 🖱️ 用户交互组件

### form
**表单组件** - 收集用户输入数据

```sql
-- 创建表单
SELECT 'form' AS component,
       '用户注册' AS title,
       'register.sql' AS action,
       'POST' AS method,
       '注册新用户' AS description;

-- 文本输入
SELECT 'name' AS name,
       'text' AS type,
       '姓名' AS label,
       '请输入您的姓名' AS placeholder,
       TRUE AS required;

-- 邮箱输入
SELECT 'email' AS name,
       'email' AS type,
       '邮箱地址' AS label,
       TRUE AS required;

-- 密码输入
SELECT 'password' AS name,
       'password' AS type,
       '密码' AS label,
       8 AS minlength;

-- 选择框
SELECT 'country' AS name,
       'select' AS type,
       '国家' AS label,
       'China' AS value,
       'China,USA,Japan,Germany' AS options;

-- 文件上传
SELECT 'avatar' AS name,
       'file' AS type,
       '头像' AS label,
       'image/*' AS accept;

-- 提交按钮
SELECT 'submit' AS name,
       'submit' AS type,
       '注册' AS value,
       'primary' AS color;
```

**输入类型**:
- `text`: 文本输入
- `email`: 邮箱输入
- `password`: 密码输入
- `number`: 数字输入
- `date`: 日期选择
- `select`: 下拉选择
- `textarea`: 多行文本
- `checkbox`: 复选框
- `radio`: 单选按钮
- `file`: 文件上传

### button
**按钮组件** - 创建各种样式的按钮

```sql
SELECT 'button' AS component;

SELECT '主要操作' AS title,
       'primary' AS color,
       '/action.sql' AS link,
       'plus' AS icon;

SELECT '次要操作' AS title,
       'secondary' AS color,
       '/secondary.sql' AS link;

SELECT '危险操作' AS title,
       'danger' AS color,
       '/delete.sql' AS link,
       'trash' AS icon,
       TRUE AS confirm;
```

---

## 🔐 认证安全组件

### authentication
**用户认证组件** - 创建受密码保护的页面

```sql
-- 检查用户认证
SELECT 'authentication' AS component,
       'login.sql' AS link,
       'Please log in to access this page' AS message
WHERE NOT EXISTS (
    SELECT 1 FROM user_sessions 
    WHERE session_token = sqlpage.cookie('session') 
    AND expires_at > datetime('now')
);

-- 页面内容（只有认证用户才能看到）
SELECT 'title' AS component, '管理面板' AS contents;
```

### cookie
**Cookie 管理组件** - 设置和管理 Cookie

```sql
-- 设置会话 Cookie
SELECT 'cookie' AS component,
       'session_token' AS name,
       sqlpage.random_string(32) AS value,
       3600 AS max_age,
       '/' AS path,
       TRUE AS secure,
       TRUE AS http_only;
```

---

## 📁 数据处理组件

### csv
**CSV 下载组件** - 让用户下载数据为 CSV 文件

```sql
-- 作为页面头部组件（直接下载）
SELECT 'csv' AS component,
       'users_export.csv' AS filename;

SELECT name, email, created_at
FROM users;
```

### json
**JSON API 组件** - 输出 JSON 格式数据

```sql
-- 必须在页面顶部使用
SELECT 'json' AS component;

SELECT id, name, email, created_at
FROM users
WHERE active = TRUE;
```

### redirect
**重定向组件** - 页面跳转

```sql
-- 条件重定向
SELECT 'redirect' AS component,
       'login.sql' AS link
WHERE sqlpage.cookie('session') IS NULL;

-- 无条件重定向
SELECT 'redirect' AS component,
       'https://example.com' AS link;
```

---

## 🎯 高级用法

### 动态组件渲染

```sql
-- 使用 dynamic 组件渲染其他组件
SELECT 'dynamic' AS component,
       sqlpage.run_sql('common_header.sql') AS properties;
```

### 组件嵌套

```sql
-- 在卡片中嵌入图表
SELECT 'card' AS component,
       2 AS columns;

SELECT '/chart1.sql?_sqlpage_embed' AS embed;
SELECT '/chart2.sql?_sqlpage_embed' AS embed;
```

### 条件显示

```sql
-- 根据条件显示不同内容
SELECT 'alert' AS component,
       CASE 
           WHEN error_count > 0 THEN 'danger'
           ELSE 'success'
       END AS color,
       CASE 
           WHEN error_count > 0 THEN '发现错误'
           ELSE '一切正常'
       END AS title
FROM system_status;
```

---

## 💡 最佳实践

1. **合理使用 shell 组件** - 每个页面都应该有一个 shell 组件设置基本属性
2. **保持数据结构清晰** - 使用有意义的列名作为参数名
3. **适当使用样式** - 通过 CSS 类和颜色参数美化界面
4. **注意性能** - 大数据量时考虑分页和过滤
5. **安全第一** - 始终验证用户输入，使用认证组件保护敏感页面

## 📚 完整组件列表

### 数据展示类
- `alert` - 警告和通知消息
- `big_number` - 关键指标和统计数据
- `breadcrumb` - 面包屑导航
- `carousel` - 图片轮播
- `code` - 代码块显示
- `datagrid` - 键值对数据网格
- `debug` - 调试信息显示
- `empty_state` - 空状态占位符
- `text` - 文本段落
- `timeline` - 时间线
- `title` - 标题
- `tracking` - 活动日志

### 导航类
- `steps` - 步骤指示器
- `tab` - 标签页

### 地图类
- `map` - 交互式地图

### 系统类
- `html` - 原始 HTML 内容
- `http_header` - HTTP 头设置
- `rss` - RSS 订阅源
- `status_code` - HTTP 状态码

更多详细信息和高级用法，请参考官方文档：https://sql-page.com/components.sql
