# SQLPage 函数参考手册

> SQLPage 提供了丰富的内置函数，帮助您处理 HTTP 请求、文件操作、系统交互等任务。

## 🔧 函数分类

### 📡 请求处理函数
- [`header()`](#header) - 获取单个 HTTP 请求头
- [`headers()`](#headers) - 获取所有 HTTP 请求头
- [`cookie()`](#cookie) - 获取 Cookie 值
- [`variables()`](#variables) - 获取 URL 参数
- [`request_method()`](#request_method) - 获取请求方法
- [`request_body()`](#request_body) - 获取请求体内容
- [`client_ip()`](#client_ip) - 获取客户端 IP 地址
- [`protocol()`](#protocol) - 获取协议类型

### 📁 文件操作函数
- [`uploaded_file_path()`](#uploaded_file_path) - 上传文件路径
- [`uploaded_file_name()`](#uploaded_file_name) - 上传文件名
- [`uploaded_file_mime_type()`](#uploaded_file_mime_type) - 上传文件类型
- [`persist_uploaded_file()`](#persist_uploaded_file) - 保存上传文件
- [`read_file_as_text()`](#read_file_as_text) - 读取文件为文本
- [`read_file_as_data_url()`](#read_file_as_data_url) - 读取文件为数据 URL

### 🔐 安全认证函数
- [`hash_password()`](#hash_password) - 密码哈希
- [`basic_auth_username()`](#basic_auth_username) - HTTP 基本认证用户名
- [`basic_auth_password()`](#basic_auth_password) - HTTP 基本认证密码
- [`user_info()`](#user_info) - 用户信息（SSO）
- [`user_info_token()`](#user_info_token) - 用户令牌（SSO）

### 🌐 网络通信函数
- [`fetch()`](#fetch) - HTTP 请求
- [`fetch_with_meta()`](#fetch_with_meta) - 带元数据的 HTTP 请求

### ⚙️ 系统功能函数
- [`exec()`](#exec) - 执行系统命令
- [`run_sql()`](#run_sql) - 执行其他 SQL 文件
- [`environment_variable()`](#environment_variable) - 获取环境变量
- [`current_working_directory()`](#current_working_directory) - 获取工作目录
- [`version()`](#version) - 获取 SQLPage 版本

### 🔧 工具函数
- [`random_string()`](#random_string) - 生成随机字符串
- [`url_encode()`](#url_encode) - URL 编码
- [`link()`](#link) - 生成链接
- [`path()`](#path) - 获取当前路径

---

## 📡 请求处理函数

### header()
**获取单个 HTTP 请求头**

```sql
-- 获取用户代理
SELECT sqlpage.header('User-Agent') AS user_agent;

-- 获取授权头
SELECT sqlpage.header('Authorization') AS auth_header;

-- 获取内容类型
SELECT sqlpage.header('Content-Type') AS content_type;
```

### headers()
**获取所有 HTTP 请求头（JSON 格式）**

```sql
-- 显示所有请求头
SELECT 'code' AS component,
       'json' AS language,
       sqlpage.headers() AS contents;

-- 在 SQLite 中解析请求头
SELECT 'list' AS component;
SELECT key AS title, value AS description
FROM json_each(sqlpage.headers());
```

### cookie()
**获取 Cookie 值**

```sql
-- 获取会话 Cookie
SELECT sqlpage.cookie('session_token') AS session;

-- 检查用户偏好
SELECT COALESCE(sqlpage.cookie('theme'), 'light') AS theme;

-- 条件检查
SELECT 'redirect' AS component, 'login.sql' AS link
WHERE sqlpage.cookie('user_id') IS NULL;
```

### variables()
**获取所有 URL 参数（JSON 格式）**

```sql
-- 显示所有 URL 参数
SELECT 'code' AS component,
       'json' AS language,
       sqlpage.variables() AS contents;

-- 获取特定参数
SELECT json_extract(sqlpage.variables(), '$.page') AS current_page;
```

---

## 📁 文件操作函数

### uploaded_file_path()
**获取上传文件的临时路径**

```sql
-- 处理文件上传
SELECT sqlpage.uploaded_file_path('avatar') AS file_path;

-- 检查文件是否上传
SELECT 'alert' AS component,
       'danger' AS color,
       '请选择文件' AS title
WHERE sqlpage.uploaded_file_path('document') IS NULL;
```

### uploaded_file_name()
**获取上传文件的原始文件名**

```sql
-- 获取文件名
SELECT sqlpage.uploaded_file_name('document') AS filename;

-- 生成新文件名
SELECT CONCAT(
    strftime('%Y%m%d_%H%M%S_', 'now'),
    sqlpage.uploaded_file_name('document')
) AS new_filename;
```

### uploaded_file_mime_type()
**获取上传文件的 MIME 类型**

```sql
-- 验证文件类型
SELECT 'alert' AS component,
       'danger' AS color,
       '只允许上传图片文件' AS title
WHERE sqlpage.uploaded_file_mime_type('image') NOT LIKE 'image/%';

-- 根据文件类型处理
SELECT 
    CASE 
        WHEN sqlpage.uploaded_file_mime_type('file') LIKE 'image/%' THEN '图片'
        WHEN sqlpage.uploaded_file_mime_type('file') LIKE 'text/%' THEN '文本'
        ELSE '其他'
    END AS file_type;
```

### persist_uploaded_file()
**保存上传文件到指定位置**

```sql
-- 保存上传的文件
SELECT sqlpage.persist_uploaded_file(
    'document',
    CONCAT('uploads/', sqlpage.random_string(16), '_', sqlpage.uploaded_file_name('document'))
) AS saved_path;
```

---

## 🔐 安全认证函数

### hash_password()
**对密码进行哈希处理**

```sql
-- 用户注册时哈希密码
INSERT INTO users (username, password_hash)
VALUES ($username, sqlpage.hash_password($password));

-- 验证密码
SELECT id FROM users 
WHERE username = $username 
AND password_hash = sqlpage.hash_password($password);
```

### basic_auth_username() / basic_auth_password()
**HTTP 基本认证**

```sql
-- 获取基本认证信息
SELECT sqlpage.basic_auth_username() AS username,
       sqlpage.basic_auth_password() AS password;

-- 验证基本认证
SELECT 'authentication' AS component,
       'Unauthorized' AS message
WHERE sqlpage.basic_auth_username() != 'admin'
   OR sqlpage.basic_auth_password() != 'secret';
```

---

## 🌐 网络通信函数

### fetch()
**发起 HTTP 请求**

```sql
-- 获取外部 API 数据
SELECT 'card' AS component;
SELECT 
    json_extract(value, '$.name') AS title,
    json_extract(value, '$.email') AS description
FROM json_each(sqlpage.fetch('https://jsonplaceholder.typicode.com/users'));

-- POST 请求
SELECT sqlpage.fetch(
    'https://api.example.com/data',
    json_object(
        'method', 'POST',
        'headers', json_object('Content-Type', 'application/json'),
        'body', json_object('key', 'value')
    )
) AS response;
```

### fetch_with_meta()
**带元数据的 HTTP 请求**

```sql
-- 获取响应状态和内容
SET response = sqlpage.fetch_with_meta('https://api.example.com/status');

SELECT 
    json_extract($response, '$.status') AS status_code,
    json_extract($response, '$.body') AS response_body,
    json_extract($response, '$.headers') AS response_headers;
```

---

## ⚙️ 系统功能函数

### exec()
**执行系统命令**（需要在配置中启用）

```sql
-- 执行 shell 命令
SELECT sqlpage.exec('ls', '-la') AS directory_listing;

-- 使用 curl 获取数据
SELECT 'card' AS component;
SELECT 
    json_extract(value, '$.name') AS title,
    json_extract(value, '$.email') AS description
FROM json_each(sqlpage.exec('curl', 'https://jsonplaceholder.typicode.com/users'));
```

### run_sql()
**执行其他 SQL 文件**

```sql
-- 包含通用头部
SELECT 'dynamic' AS component,
       sqlpage.run_sql('common_header.sql') AS properties;

-- 带参数执行
SELECT 'dynamic' AS component,
       sqlpage.run_sql(
           'user_profile.sql',
           json_object('user_id', $user_id)
       ) AS properties;
```

---

## 🔧 工具函数

### random_string()
**生成随机字符串**

```sql
-- 生成会话令牌
SELECT sqlpage.random_string(32) AS session_token;

-- 生成文件名
SELECT CONCAT(
    'upload_',
    sqlpage.random_string(16),
    '.tmp'
) AS temp_filename;
```

### url_encode()
**URL 编码**

```sql
-- 编码 URL 参数
SELECT CONCAT(
    'search.sql?q=',
    sqlpage.url_encode($search_query)
) AS search_url;

-- 编码特殊字符
SELECT sqlpage.url_encode('Hello World!') AS encoded; -- 结果: Hello%20World%21
```

---

## 💡 实用示例

### 文件上传处理完整流程

```sql
-- 检查文件是否上传
SELECT 'redirect' AS component, 'upload_form.sql?error=no_file' AS link
WHERE sqlpage.uploaded_file_path('document') IS NULL;

-- 验证文件类型
SELECT 'redirect' AS component, 'upload_form.sql?error=invalid_type' AS link
WHERE sqlpage.uploaded_file_mime_type('document') NOT IN ('application/pdf', 'image/jpeg', 'image/png');

-- 保存文件
SET saved_path = sqlpage.persist_uploaded_file(
    'document',
    CONCAT('uploads/', sqlpage.random_string(16), '_', sqlpage.uploaded_file_name('document'))
);

-- 记录到数据库
INSERT INTO uploaded_files (filename, path, mime_type, uploaded_at)
VALUES (
    sqlpage.uploaded_file_name('document'),
    $saved_path,
    sqlpage.uploaded_file_mime_type('document'),
    datetime('now')
);

-- 成功页面
SELECT 'alert' AS component,
       'success' AS color,
       '文件上传成功' AS title;
```

### API 数据获取和处理

```sql
-- 获取外部数据
SET api_response = sqlpage.fetch('https://api.github.com/repos/sqlpage/SQLPage');

-- 解析和显示
SELECT 'card' AS component;
SELECT 
    json_extract($api_response, '$.name') AS title,
    json_extract($api_response, '$.description') AS description,
    CONCAT('⭐ ', json_extract($api_response, '$.stargazers_count')) AS footer;
```

### 用户会话管理

```sql
-- 检查会话
SET session_token = sqlpage.cookie('session');
SET user_id = (
    SELECT user_id FROM sessions 
    WHERE token = $session_token 
    AND expires_at > datetime('now')
);

-- 未登录重定向
SELECT 'redirect' AS component, 'login.sql' AS link
WHERE $user_id IS NULL;

-- 更新会话过期时间
UPDATE sessions 
SET expires_at = datetime('now', '+1 hour')
WHERE token = $session_token;
```

---

## ⚠️ 注意事项

1. **安全性**: 
   - 始终验证用户输入
   - 谨慎使用 `exec()` 函数
   - 不要在 `exec()` 中使用用户提供的参数

2. **性能**:
   - `fetch()` 函数会阻塞请求，避免长时间的网络调用
   - 大文件上传时注意内存使用

3. **错误处理**:
   - 检查函数返回值是否为 NULL
   - 使用条件语句处理异常情况

4. **数据库兼容性**:
   - JSON 函数在不同数据库中语法可能不同
   - 参考各数据库的 JSON 处理文档

更多详细信息请参考官方文档：https://sql-page.com/functions.sql
